name: ✨ Feature Request
description: Suggest a new feature or enhancement for SF CLI
title: "[Feature]: "
labels: ["enhancement", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new feature! Please fill out the form below to help us understand your request.

  - type: textarea
    id: problem
    attributes:
      label: Problem Description
      description: Is your feature request related to a problem? Please describe.
      placeholder: "I'm always frustrated when..."
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like to see implemented.
      placeholder: "I would like SF CLI to..."
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternative Solutions
      description: Describe any alternative solutions or features you've considered.
      placeholder: "Alternatively, we could..."

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would be helpful
        - High - Important for my workflow
        - Critical - Blocking my work
    validations:
      required: true

  - type: dropdown
    id: category
    attributes:
      label: Feature Category
      description: What category does this feature belong to?
      options:
        - Code Generation
        - Project Structure
        - CLI Interface
        - Documentation
        - Build Process
        - Configuration
        - Other
    validations:
      required: true

  - type: textarea
    id: use-case
    attributes:
      label: Use Case
      description: Describe your specific use case for this feature.
      placeholder: "I need this feature because..."
    validations:
      required: true

  - type: textarea
    id: examples
    attributes:
      label: Examples
      description: Provide examples of how this feature would work.
      placeholder: |
        Command: sf_cli new-feature --example
        Output: ...
      render: shell

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context, screenshots, or examples about the feature request.
      placeholder: "Any additional information..."

  - type: checkboxes
    id: terms
    attributes:
      label: Checklist
      description: Please confirm the following
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have provided a clear description of the feature
          required: true
        - label: This feature would benefit other SF CLI users
          required: true
