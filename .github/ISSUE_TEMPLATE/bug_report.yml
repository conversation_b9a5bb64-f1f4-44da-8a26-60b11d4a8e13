name: 🐛 Bug Report
description: Report a bug or issue with SF CLI
title: "[Bug]: "
labels: ["bug", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug! Please fill out the form below to help us understand and fix the issue.

  - type: input
    id: version
    attributes:
      label: SF CLI Version
      description: What version of SF CLI are you using?
      placeholder: "1.0.1"
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you using?
      options:
        - macOS
        - Windows
        - Linux
        - Other
    validations:
      required: true

  - type: input
    id: dart-version
    attributes:
      label: Dart SDK Version
      description: What version of Dart SDK are you using?
      placeholder: "3.4.3"
    validations:
      required: true

  - type: input
    id: flutter-version
    attributes:
      label: Flutter Version
      description: What version of Flutter are you using?
      placeholder: "3.22.0"
    validations:
      required: false

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: "Describe the bug..."
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Run command '...'
        2. See error
      value: |
        1. 
        2. 
        3. 
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: "What should have happened?"
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: A clear and concise description of what actually happened.
      placeholder: "What actually happened?"
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Error Logs
      description: If applicable, add error logs or command output
      render: shell
      placeholder: "Paste error logs here..."

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context about the problem here.
      placeholder: "Any additional information..."

  - type: checkboxes
    id: terms
    attributes:
      label: Checklist
      description: Please confirm the following
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have provided all the required information above
          required: true
        - label: I am using the latest version of SF CLI
          required: false
