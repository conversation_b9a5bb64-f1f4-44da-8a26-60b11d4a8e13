# Pull Request

## Description

Brief description of the changes in this PR.

## Type of Change

Please delete options that are not relevant.

- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Refactoring (no functional changes)
- [ ] ⚡ Performance improvement
- [ ] 🧪 Test addition or improvement

## Changes Made

- [ ] Change 1
- [ ] Change 2
- [ ] Change 3

## Testing

- [ ] Tests added/updated for new functionality
- [ ] All existing tests pass
- [ ] Manual testing completed

### Test Commands Run

```bash
# Add the commands you used to test your changes
dart test
sf_cli --help
```

## Screenshots (if applicable)

<!-- Add screenshots here if your changes affect the UI or output -->

## Checklist

- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## Related Issues

Fixes #(issue number)
Closes #(issue number)
Related to #(issue number)

## Additional Notes

<!-- Any additional information that reviewers should know -->

## Breaking Changes

<!-- If this is a breaking change, describe what users need to do to migrate -->

## Documentation

- [ ] README updated (if needed)
- [ ] Documentation site updated (if needed)
- [ ] CHANGELOG updated
- [ ] API documentation updated (if needed)
