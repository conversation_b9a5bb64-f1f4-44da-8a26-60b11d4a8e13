<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>SF CLI Documentation</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="description" content="SF CLI - Flutter Scaffolding Tool Documentation">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/vue.css">
  <link rel="icon" href="_media/favicon.ico">
</head>
<body>
  <div id="app">Loading...</div>
  <script>
    window.$docsify = {
      name: 'SF CLI',
      repo: 'https://github.com/naveenld024/sf_cli',
      homepage: 'README.md',
      loadSidebar: true,
      loadNavbar: true,
      subMaxLevel: 3,
      auto2top: true,
      search: {
        maxAge: 86400000, // Expiration time, the default one day
        paths: 'auto',
        placeholder: 'Search documentation...',
        noData: 'No Results!',
        depth: 6,
        hideOtherSidebarContent: false,
      },
      copyCode: {
        buttonText: 'Copy to clipboard',
        errorText: 'Error',
        successText: 'Copied'
      },
      pagination: {
        previousText: 'Previous',
        nextText: 'Next',
        crossChapter: true,
        crossChapterText: true,
      },
      tabs: {
        persist: true,
        sync: true,
        theme: 'classic',
        tabComments: true,
        tabHeadings: true
      },
      themeable: {
        readyTransition: true,
        responsiveTables: true
      },
      plugins: [
        function(hook, vm) {
          hook.beforeEach(function (html) {
            var url = 'https://github.com/naveenld024/sf_cli/blob/main/docs/' + vm.route.file
            var editHtml = '[:memo: Edit Document](' + url + ')\n'
            return editHtml + html
          })
        }
      ]
    }
  </script>
  <!-- Docsify v4 -->
  <script src="//cdn.jsdelivr.net/npm/docsify@4"></script>
  <!-- Search plugin -->
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/search.min.js"></script>
  <!-- Copy code plugin -->
  <script src="//cdn.jsdelivr.net/npm/docsify-copy-code@2"></script>
  <!-- Pagination plugin -->
  <script src="//cdn.jsdelivr.net/npm/docsify-pagination/dist/docsify-pagination.min.js"></script>
  <!-- Tabs plugin -->
  <script src="//cdn.jsdelivr.net/npm/docsify-tabs@1"></script>
  <!-- Themeable plugin -->
  <script src="//cdn.jsdelivr.net/npm/docsify-themeable@0"></script>
  <!-- Prism syntax highlighting -->
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-bash.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-dart.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-json.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-yaml.min.js"></script>
</body>
</html>
