<!-- docs/_sidebar.md -->

* [Home](/)

* Getting Started
  * [Installation](installation.md)
  * [Quick Start](quickstart.md)

* Commands
  * [Command Reference](commands.md)
  * [Project Initialization](commands/init.md)
  * [Feature Generation](commands/features.md)
  * [Model Generation](commands/model.md)
  * [State Management](commands/state-management.md)
  * [Build Runner](commands/runner.md)
  * [Config Generation](commands/config.md)

* Examples & Tutorials
  * [Examples Overview](examples.md)
  * [Basic Usage](examples/basic-usage.md)
  * [Advanced Features](examples/advanced-features.md)
  * [Enhanced Feature Generation](examples/enhanced-features.md)

* Architecture
  * [Architecture Overview](architecture.md)
  * [Clean Architecture](architecture/clean-architecture.md)
  * [BLoC Pattern](architecture/bloc-pattern.md)
  * [Freezed Integration](architecture/freezed.md)

* Configuration
  * [Configuration Files](configuration.md)
  * [Simple Config](configuration/simple-config.md)
  * [Enhanced Config](configuration/enhanced-config.md)

* API Reference
  * [CLI API](api.md)
  * [Generated Code Structure](api/code-structure.md)

* Contributing
  * [Contributing Guide](contributing.md)
  * [Development Setup](contributing/development.md)

* [Changelog](changelog.md)
