name: sf_cli
description: A powerful Flutter CLI tool for scaffolding features, generating models, and managing project structure with clean architecture patterns.
version: 1.0.3
homepage: https://github.com/naveenld024/sf_cli
repository: https://github.com/naveenld024/sf_cli
issue_tracker: https://github.com/naveenld024/sf_cli/issues
documentation: https://naveenld024.github.io/sf_cli/

environment:
  sdk: ^3.4.3

dependencies:
  args: ^2.3.0
  process_run: ^0.12.2
  path: ^1.8.0

dev_dependencies:
  lints: ^3.0.0
  test: ^1.24.0

executables:
  sf_cli:
