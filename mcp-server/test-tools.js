import { spawn } from 'child_process';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Test the MCP server tools list
async function testToolsList() {
  const serverPath = join(__dirname, 'index-simple.js');
  const child = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let stdout = '';
  let stderr = '';

  child.stdout.on('data', (data) => {
    stdout += data.toString();
    console.log('Server output:', data.toString());
  });

  child.stderr.on('data', (data) => {
    stderr += data.toString();
    console.error('Server error:', data.toString());
  });

  // Send a tools/list request
  const listToolsRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "tools/list",
    params: {}
  };

  console.log('Sending tools/list request...');
  child.stdin.write(JSON.stringify(listToolsRequest) + '\n');

  // Wait a bit and then close
  setTimeout(() => {
    child.kill();
    console.log('Test completed');
    console.log('Full stdout:', stdout);
    console.log('Full stderr:', stderr);
  }, 3000);
}

testToolsList().catch(console.error); 