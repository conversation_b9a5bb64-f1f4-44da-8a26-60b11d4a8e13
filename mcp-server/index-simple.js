import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { spawn } from "child_process";
import { fileURLToPath } from "url";
import { dirname, join } from "path";
import { existsSync } from "fs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const server = new Server({
  name: "SF CLI MCP Server",
  version: "1.0.0",
}, {
  capabilities: {
    tools: {}
  }
});

// Helper function to execute sf_cli commands
async function executeSfCliCommand(args) {
  return new Promise((resolve, reject) => {
    const sfCliPath = join(__dirname, '..', 'bin', 'sf_cli.dart');
    const command = existsSync(sfCliPath) ? 'dart' : 'sf_cli';
    const commandArgs = existsSync(sfCliPath) ? ['run', sfCliPath, ...args] : args;
    
    const child = spawn(command, commandArgs, {
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(stdout);
      } else {
        reject(new Error(`Command failed with code ${code}: ${stderr}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

// Define schemas for tool requests
const ToolCallRequestSchema = z.object({
  method: z.literal("tools/call"),
  params: z.object({
    name: z.string(),
    arguments: z.record(z.any())
  })
});

const ListToolsRequestSchema = z.object({
  method: z.literal("tools/list"),
  params: z.object({})
});

// Register tools list handler
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: "initialize_project",
        description: "Initialize a new Flutter project with SF CLI structure",
        inputSchema: {
          type: "object",
          properties: {}
        }
      },
      {
        name: "generate_feature",
        description: "Generate a new feature with complete folder structure",
        inputSchema: {
          type: "object",
          properties: {
            name: {
              type: "string",
              description: "Name of the feature to generate"
            },
            use_freezed: {
              type: "boolean",
              description: "Generate freezed cubit and state"
            }
          },
          required: ["name"]
        }
      },
      {
        name: "generate_model",
        description: "Generate Dart model classes from JSON files",
        inputSchema: {
          type: "object",
          properties: {
            json_file_path: {
              type: "string",
              description: "Path to the JSON file for model generation"
            }
          },
          required: ["json_file_path"]
        }
      },
      {
        name: "generate_cubit",
        description: "Generate a new Cubit class with state management",
        inputSchema: {
          type: "object",
          properties: {
            name: {
              type: "string",
              description: "Name of the cubit to generate"
            },
            use_freezed: {
              type: "boolean",
              description: "Generate freezed cubit and state"
            }
          },
          required: ["name"]
        }
      },
      {
        name: "generate_bloc",
        description: "Generate a new BLoC class with event and state management",
        inputSchema: {
          type: "object",
          properties: {
            name: {
              type: "string",
              description: "Name of the bloc to generate"
            },
            use_freezed: {
              type: "boolean",
              description: "Generate freezed bloc and event"
            }
          },
          required: ["name"]
        }
      },
      {
        name: "run_build_runner",
        description: "Run build_runner to generate code",
        inputSchema: {
          type: "object",
          properties: {}
        }
      },
      {
        name: "generate_from_config",
        description: "Generate code from configuration file",
        inputSchema: {
          type: "object",
          properties: {
            config_file_path: {
              type: "string",
              description: "Path to the configuration JSON file"
            }
          },
          required: ["config_file_path"]
        }
      },
      {
        name: "generate_feature_from_config",
        description: "Generate complete feature from enhanced configuration file",
        inputSchema: {
          type: "object",
          properties: {
            config_file_path: {
              type: "string",
              description: "Path to the enhanced configuration JSON file"
            },
            use_freezed: {
              type: "boolean",
              description: "Generate freezed models and state management"
            }
          },
          required: ["config_file_path"]
        }
      },
      {
        name: "show_help",
        description: "Show SF CLI help information",
        inputSchema: {
          type: "object",
          properties: {}
        }
      },
      {
        name: "create_sample_config",
        description: "Create sample configuration files",
        inputSchema: {
          type: "object",
          properties: {
            config_type: {
              type: "string",
              enum: ["basic", "enhanced"],
              description: "Type of configuration to create"
            },
            output_path: {
              type: "string",
              description: "Path where to save the configuration file"
            }
          },
          required: ["config_type", "output_path"]
        }
      }
    ]
  };
});

// Register tool call handler
server.setRequestHandler(ToolCallRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  
  try {
    let result;
    
    switch (name) {
      case "initialize_project":
        result = await executeSfCliCommand(['init']);
        return {
          content: [{ 
            type: "text", 
            text: `✅ Project initialized successfully!\n\n${result}` 
          }]
        };

      case "generate_feature":
        const featureArgs = ['features', '--name', args.name];
        if (args.use_freezed) {
          featureArgs.push('--freezed');
        }
        result = await executeSfCliCommand(featureArgs);
        return {
          content: [{ 
            type: "text", 
            text: `✅ Feature '${args.name}' generated successfully!\n\n${result}` 
          }]
        };

      case "generate_model":
        result = await executeSfCliCommand(['model', '--file', args.json_file_path]);
        return {
          content: [{ 
            type: "text", 
            text: `✅ Model generated from ${args.json_file_path}!\n\n${result}` 
          }]
        };

      case "generate_cubit":
        const cubitArgs = ['cubit', '--name', args.name];
        if (args.use_freezed) {
          cubitArgs.push('--freezed');
        }
        result = await executeSfCliCommand(cubitArgs);
        return {
          content: [{ 
            type: "text", 
            text: `✅ Cubit '${args.name}' generated successfully!\n\n${result}` 
          }]
        };

      case "generate_bloc":
        const blocArgs = ['bloc', '--name', args.name];
        if (args.use_freezed) {
          blocArgs.push('--freezed');
        }
        result = await executeSfCliCommand(blocArgs);
        return {
          content: [{ 
            type: "text", 
            text: `✅ BLoC '${args.name}' generated successfully!\n\n${result}` 
          }]
        };

      case "run_build_runner":
        result = await executeSfCliCommand(['runner']);
        return {
          content: [{ 
            type: "text", 
            text: `✅ Build runner completed successfully!\n\n${result}` 
          }]
        };

      case "generate_from_config":
        result = await executeSfCliCommand(['config', '--config-file', args.config_file_path]);
        return {
          content: [{ 
            type: "text", 
            text: `✅ Code generated from config ${args.config_file_path}!\n\n${result}` 
          }]
        };

      case "generate_feature_from_config":
        const featureConfigArgs = ['generate-feature', '--config', args.config_file_path];
        if (args.use_freezed) {
          featureConfigArgs.push('--freezed');
        }
        result = await executeSfCliCommand(featureConfigArgs);
        return {
          content: [{ 
            type: "text", 
            text: `✅ Complete feature generated from config ${args.config_file_path}!\n\n${result}` 
          }]
        };

      case "show_help":
        result = await executeSfCliCommand(['--help']);
        return {
          content: [{ 
            type: "text", 
            text: `📖 SF CLI Help:\n\n${result}` 
          }]
        };

      case "create_sample_config":
        const { writeFileSync } = await import('fs');
        let sampleConfig;
        
        if (args.config_type === 'basic') {
          sampleConfig = {
            "user_model": {
              "model_class_relative_path": "lib/models/user.json",
              "end_point": "/api/users",
              "method": "get",
              "function_name": "getUsers",
              "parameters": {
                "page": "int",
                "limit": "int",
                "search": "String"
              }
            }
          };
        } else {
          sampleConfig = {
            "feature_name": "user_profile",
            "description": "A complete user profile feature",
            "models": [
              {
                "name": "user",
                "json_file_path": "lib/models/user.json",
                "use_freezed": true
              }
            ],
            "endpoints": [
              {
                "name": "get_user_profile",
                "endpoint": "/api/users/{id}",
                "method": "GET",
                "function_name": "getUserProfile",
                "parameters": {
                  "id": "int"
                },
                "return_type": "ResponseResult<User>"
              }
            ],
            "ui_config": {
              "has_list_screen": false,
              "has_detail_screen": true,
              "has_form_screen": true,
              "custom_widgets": [
                "profile_card",
                "avatar_upload"
              ]
            }
          };
        }
        
        writeFileSync(args.output_path, JSON.stringify(sampleConfig, null, 2));
        
        return {
          content: [{ 
            type: "text", 
            text: `✅ Sample ${args.config_type} configuration created at ${args.output_path}!\n\nConfiguration:\n${JSON.stringify(sampleConfig, null, 2)}` 
          }]
        };

      default:
        return {
          content: [{ 
            type: "text", 
            text: `❌ Unknown tool: ${name}` 
          }]
        };
    }
  } catch (error) {
    return {
      content: [{ 
        type: "text", 
        text: `❌ Failed to execute ${name}: ${error.message}` 
      }]
    };
  }
});

const transport = new StdioServerTransport();
await server.connect(transport); 