import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { spawn } from "child_process";
import { fileURLToPath } from "url";
import { dirname, join } from "path";
import { existsSync, readFileSync, writeFileSync } from "fs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const server = new Server({
  name: "SF CLI MCP Server",
  version: "1.0.0",
}, {
  capabilities: {
    tools: {}
  }
});

// Helper function to execute sf_cli commands
async function executeSfCliCommand(args) {
  return new Promise((resolve, reject) => {
    // Try to find sf_cli in the current directory or use dart run
    const sfCliPath = join(__dirname, '..', 'bin', 'sf_cli.dart');
    const command = existsSync(sfCliPath) ? 'dart' : 'sf_cli';
    const commandArgs = existsSync(sfCliPath) ? ['run', sfCliPath, ...args] : args;
    
    const child = spawn(command, commandArgs, {
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(stdout);
      } else {
        reject(new Error(`Command failed with code ${code}: ${stderr}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

// Define tool schemas
const InitializeProjectRequestSchema = z.object({
  method: z.literal("tools/call"),
  params: z.object({
    name: z.literal("initialize_project"),
    arguments: z.object({})
  })
});

const GenerateFeatureRequestSchema = z.object({
  method: z.literal("tools/call"),
  params: z.object({
    name: z.literal("generate_feature"),
    arguments: z.object({
      name: z.string(),
      use_freezed: z.boolean().optional()
    })
  })
});

const GenerateModelRequestSchema = z.object({
  method: z.literal("tools/call"),
  params: z.object({
    name: z.literal("generate_model"),
    arguments: z.object({
      json_file_path: z.string()
    })
  })
});

const GenerateCubitRequestSchema = z.object({
  method: z.literal("tools/call"),
  params: z.object({
    name: z.literal("generate_cubit"),
    arguments: z.object({
      name: z.string(),
      use_freezed: z.boolean().optional()
    })
  })
});

const GenerateBlocRequestSchema = z.object({
  method: z.literal("tools/call"),
  params: z.object({
    name: z.literal("generate_bloc"),
    arguments: z.object({
      name: z.string(),
      use_freezed: z.boolean().optional()
    })
  })
});

const RunBuildRunnerRequestSchema = z.object({
  method: z.literal("tools/call"),
  params: z.object({
    name: z.literal("run_build_runner"),
    arguments: z.object({})
  })
});

const GenerateFromConfigRequestSchema = z.object({
  method: z.literal("tools/call"),
  params: z.object({
    name: z.literal("generate_from_config"),
    arguments: z.object({
      config_file_path: z.string()
    })
  })
});

const GenerateFeatureFromConfigRequestSchema = z.object({
  method: z.literal("tools/call"),
  params: z.object({
    name: z.literal("generate_feature_from_config"),
    arguments: z.object({
      config_file_path: z.string(),
      use_freezed: z.boolean().optional()
    })
  })
});

const ShowHelpRequestSchema = z.object({
  method: z.literal("tools/call"),
  params: z.object({
    name: z.literal("show_help"),
    arguments: z.object({})
  })
});

const CreateSampleConfigRequestSchema = z.object({
  method: z.literal("tools/call"),
  params: z.object({
    name: z.literal("create_sample_config"),
    arguments: z.object({
      config_type: z.enum(['basic', 'enhanced']),
      output_path: z.string()
    })
  })
});

// Register tool handlers
server.setRequestHandler(InitializeProjectRequestSchema, async () => {
  try {
    const result = await executeSfCliCommand(['init']);
    return {
      content: [{ 
        type: "text", 
        text: `✅ Project initialized successfully!\n\n${result}` 
      }]
    };
  } catch (error) {
    return {
      content: [{ 
        type: "text", 
        text: `❌ Failed to initialize project: ${error.message}` 
      }]
    };
  }
});

server.setRequestHandler(GenerateFeatureRequestSchema, async (request) => {
  try {
    const { name, use_freezed } = request.params.arguments;
    const args = ['features', '--name', name];
    if (use_freezed) {
      args.push('--freezed');
    }
    
    const result = await executeSfCliCommand(args);
    return {
      content: [{ 
        type: "text", 
        text: `✅ Feature '${name}' generated successfully!\n\n${result}` 
      }]
    };
  } catch (error) {
    return {
      content: [{ 
        type: "text", 
        text: `❌ Failed to generate feature: ${error.message}` 
      }]
    };
  }
});

server.setRequestHandler(GenerateModelRequestSchema, async (request) => {
  try {
    const { json_file_path } = request.params.arguments;
    const result = await executeSfCliCommand(['model', '--file', json_file_path]);
    return {
      content: [{ 
        type: "text", 
        text: `✅ Model generated from ${json_file_path}!\n\n${result}` 
      }]
    };
  } catch (error) {
    return {
      content: [{ 
        type: "text", 
        text: `❌ Failed to generate model: ${error.message}` 
      }]
    };
  }
});

server.setRequestHandler(GenerateCubitRequestSchema, async (request) => {
  try {
    const { name, use_freezed } = request.params.arguments;
    const args = ['cubit', '--name', name];
    if (use_freezed) {
      args.push('--freezed');
    }
    
    const result = await executeSfCliCommand(args);
    return {
      content: [{ 
        type: "text", 
        text: `✅ Cubit '${name}' generated successfully!\n\n${result}` 
      }]
    };
  } catch (error) {
    return {
      content: [{ 
        type: "text", 
        text: `❌ Failed to generate cubit: ${error.message}` 
      }]
    };
  }
});

server.setRequestHandler(GenerateBlocRequestSchema, async (request) => {
  try {
    const { name, use_freezed } = request.params.arguments;
    const args = ['bloc', '--name', name];
    if (use_freezed) {
      args.push('--freezed');
    }
    
    const result = await executeSfCliCommand(args);
    return {
      content: [{ 
        type: "text", 
        text: `✅ BLoC '${name}' generated successfully!\n\n${result}` 
      }]
    };
  } catch (error) {
    return {
      content: [{ 
        type: "text", 
        text: `❌ Failed to generate BLoC: ${error.message}` 
      }]
    };
  }
});

server.setRequestHandler(RunBuildRunnerRequestSchema, async () => {
  try {
    const result = await executeSfCliCommand(['runner']);
    return {
      content: [{ 
        type: "text", 
        text: `✅ Build runner completed successfully!\n\n${result}` 
      }]
    };
  } catch (error) {
    return {
      content: [{ 
        type: "text", 
        text: `❌ Build runner failed: ${error.message}` 
      }]
    };
  }
});

server.setRequestHandler(GenerateFromConfigRequestSchema, async (request) => {
  try {
    const { config_file_path } = request.params.arguments;
    const result = await executeSfCliCommand(['config', '--config-file', config_file_path]);
    return {
      content: [{ 
        type: "text", 
        text: `✅ Code generated from config ${config_file_path}!\n\n${result}` 
      }]
    };
  } catch (error) {
    return {
      content: [{ 
        type: "text", 
        text: `❌ Failed to generate from config: ${error.message}` 
      }]
    };
  }
});

server.setRequestHandler(GenerateFeatureFromConfigRequestSchema, async (request) => {
  try {
    const { config_file_path, use_freezed } = request.params.arguments;
    const args = ['generate-feature', '--config', config_file_path];
    if (use_freezed) {
      args.push('--freezed');
    }
    
    const result = await executeSfCliCommand(args);
    return {
      content: [{ 
        type: "text", 
        text: `✅ Complete feature generated from config ${config_file_path}!\n\n${result}` 
      }]
    };
  } catch (error) {
    return {
      content: [{ 
        type: "text", 
        text: `❌ Failed to generate feature from config: ${error.message}` 
      }]
    };
  }
});

server.setRequestHandler(ShowHelpRequestSchema, async () => {
  try {
    const result = await executeSfCliCommand(['--help']);
    return {
      content: [{ 
        type: "text", 
        text: `📖 SF CLI Help:\n\n${result}` 
      }]
    };
  } catch (error) {
    return {
      content: [{ 
        type: "text", 
        text: `❌ Failed to show help: ${error.message}` 
      }]
    };
  }
});

server.setRequestHandler(CreateSampleConfigRequestSchema, async (request) => {
  try {
    const { config_type, output_path } = request.params.arguments;
    let sampleConfig;
    
    if (config_type === 'basic') {
      sampleConfig = {
        "user_model": {
          "model_class_relative_path": "lib/models/user.json",
          "end_point": "/api/users",
          "method": "get",
          "function_name": "getUsers",
          "parameters": {
            "page": "int",
            "limit": "int",
            "search": "String"
          }
        }
      };
    } else {
      sampleConfig = {
        "feature_name": "user_profile",
        "description": "A complete user profile feature",
        "models": [
          {
            "name": "user",
            "json_file_path": "lib/models/user.json",
            "use_freezed": true
          }
        ],
        "endpoints": [
          {
            "name": "get_user_profile",
            "endpoint": "/api/users/{id}",
            "method": "GET",
            "function_name": "getUserProfile",
            "parameters": {
              "id": "int"
            },
            "return_type": "ResponseResult<User>"
          }
        ],
        "ui_config": {
          "has_list_screen": false,
          "has_detail_screen": true,
          "has_form_screen": true,
          "custom_widgets": [
            "profile_card",
            "avatar_upload"
          ]
        }
      };
    }
    
    writeFileSync(output_path, JSON.stringify(sampleConfig, null, 2));
    
    return {
      content: [{ 
        type: "text", 
        text: `✅ Sample ${config_type} configuration created at ${output_path}!\n\nConfiguration:\n${JSON.stringify(sampleConfig, null, 2)}` 
      }]
    };
  } catch (error) {
    return {
      content: [{ 
        type: "text", 
        text: `❌ Failed to create sample config: ${error.message}` 
      }]
    };
  }
});

// Register tools list handler
const ListToolsRequestSchema = z.object({
  method: z.literal("tools/list"),
  params: z.object({})
});

server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: "initialize_project",
        description: "Initialize a new Flutter project with SF CLI structure",
        inputSchema: {
          type: "object",
          properties: {}
        }
      },
      {
        name: "generate_feature",
        description: "Generate a new feature with complete folder structure",
        inputSchema: {
          type: "object",
          properties: {
            name: {
              type: "string",
              description: "Name of the feature to generate"
            },
            use_freezed: {
              type: "boolean",
              description: "Generate freezed cubit and state"
            }
          },
          required: ["name"]
        }
      },
      {
        name: "generate_model",
        description: "Generate Dart model classes from JSON files",
        inputSchema: {
          type: "object",
          properties: {
            json_file_path: {
              type: "string",
              description: "Path to the JSON file for model generation"
            }
          },
          required: ["json_file_path"]
        }
      },
      {
        name: "generate_cubit",
        description: "Generate a new Cubit class with state management",
        inputSchema: {
          type: "object",
          properties: {
            name: {
              type: "string",
              description: "Name of the cubit to generate"
            },
            use_freezed: {
              type: "boolean",
              description: "Generate freezed cubit and state"
            }
          },
          required: ["name"]
        }
      },
      {
        name: "generate_bloc",
        description: "Generate a new BLoC class with event and state management",
        inputSchema: {
          type: "object",
          properties: {
            name: {
              type: "string",
              description: "Name of the bloc to generate"
            },
            use_freezed: {
              type: "boolean",
              description: "Generate freezed bloc and event"
            }
          },
          required: ["name"]
        }
      },
      {
        name: "run_build_runner",
        description: "Run build_runner to generate code",
        inputSchema: {
          type: "object",
          properties: {}
        }
      },
      {
        name: "generate_from_config",
        description: "Generate code from configuration file",
        inputSchema: {
          type: "object",
          properties: {
            config_file_path: {
              type: "string",
              description: "Path to the configuration JSON file"
            }
          },
          required: ["config_file_path"]
        }
      },
      {
        name: "generate_feature_from_config",
        description: "Generate complete feature from enhanced configuration file",
        inputSchema: {
          type: "object",
          properties: {
            config_file_path: {
              type: "string",
              description: "Path to the enhanced configuration JSON file"
            },
            use_freezed: {
              type: "boolean",
              description: "Generate freezed models and state management"
            }
          },
          required: ["config_file_path"]
        }
      },
      {
        name: "show_help",
        description: "Show SF CLI help information",
        inputSchema: {
          type: "object",
          properties: {}
        }
      },
      {
        name: "create_sample_config",
        description: "Create sample configuration files",
        inputSchema: {
          type: "object",
          properties: {
            config_type: {
              type: "string",
              enum: ["basic", "enhanced"],
              description: "Type of configuration to create"
            },
            output_path: {
              type: "string",
              description: "Path where to save the configuration file"
            }
          },
          required: ["config_type", "output_path"]
        }
      }
    ]
  };
});

const transport = new StdioServerTransport();
await server.connect(transport); 