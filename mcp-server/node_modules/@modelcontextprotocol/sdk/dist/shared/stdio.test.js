import { ReadBuffer } from "./stdio.js";
const testMessage = {
    jsonrpc: "2.0",
    method: "foobar",
};
test("should have no messages after initialization", () => {
    const readBuffer = new ReadBuffer();
    expect(readBuffer.readMessage()).toBeNull();
});
test("should only yield a message after a newline", () => {
    const readBuffer = new ReadBuffer();
    readBuffer.append(Buffer.from(JSON.stringify(testMessage)));
    expect(readBuffer.readMessage()).toBeNull();
    readBuffer.append(Buffer.from("\n"));
    expect(readBuffer.readMessage()).toEqual(testMessage);
    expect(readBuffer.readMessage()).toBeNull();
});
test("should be reusable after clearing", () => {
    const readBuffer = new ReadBuffer();
    readBuffer.append(Buffer.from("foobar"));
    readBuffer.clear();
    expect(readBuffer.readMessage()).toBeNull();
    readBuffer.append(Buffer.from(JSON.stringify(testMessage)));
    readBuffer.append(Buffer.from("\n"));
    expect(readBuffer.readMessage()).toEqual(testMessage);
});
//# sourceMappingURL=stdio.test.js.map