# SF CLI MCP Server Troubleshooting Guide

## 🚨 Issue: "0 tools enabled" in Cursor

**Status**: Server is working correctly (confirmed by tests), but <PERSON>urs<PERSON> is not recognizing the tools.

## ✅ Verified Working Components

1. **Server File**: `index-simple.js` exists and is accessible
2. **MCP Protocol**: Server responds correctly to all MCP requests
3. **Tools Registration**: All 10 tools are properly registered and returned
4. **Configuration**: `~/.cursor/mcp.json` points to correct server path
5. **Permissions**: File has correct read permissions

## 🔧 Step-by-Step Resolution

### Step 1: Complete Cursor Restart
1. **Quit Cursor completely** (Cmd+Q on Mac)
2. **Wait 10 seconds**
3. **Restart Cursor**
4. **Check MCP section** - should show "10 tools enabled"

### Step 2: If Still "0 tools", Check MCP Logs
1. Open Cursor
2. Open Output panel: `Ctrl+Shift+U` (Windows/Linux) or `Cmd+Shift+U` (Mac)
3. Select "MCP Logs" from dropdown
4. Look for any error messages related to `sf-cli-mcp`

### Step 3: Disable and Re-enable MCP Server
1. Open Cursor Settings: `Ctrl+Shift+J` (Windows/Linux) or `Cmd+Shift+J` (Mac)
2. Navigate to Features → Model Context Protocol
3. Find `sf-cli-mcp` entry
4. **Toggle it OFF**
5. **Wait 5 seconds**
6. **Toggle it ON**
7. Check if tools count updates

### Step 4: Verify Configuration
Your `~/.cursor/mcp.json` should contain:
```json
{
  "mcpServers": {
    "Framelink Figma MCP": {
      "command": "npx",
      "args": [
        "-y",
        "figma-developer-mcp",
        "--figma-api-key=*********************************************",
        "--stdio"
      ]
    },
    "sf-cli-mcp": {
      "command": "node",
      "args": ["/Volumes/SSD/loom_dynamics/sf_cli/mcp-server/index-simple.js"]
    }
  }
}
```

### Step 5: Test Server Manually
Run this command to verify server is working:
```bash
cd /Volumes/SSD/loom_dynamics/sf_cli/mcp-server
node debug-mcp.js
```

Expected output should show:
- ✅ Server file exists
- ✅ Found 10 tools in response
- ✅ Tool calls working

### Step 6: Check for Conflicting Configurations
1. Check for project-specific config:
   ```bash
   ls -la /Volumes/SSD/loom_dynamics/sf_cli/.cursor/mcp.json
   ```
2. If it exists, either remove it or ensure it matches the global config

### Step 7: Alternative Server Path
If the issue persists, try using a relative path:
```json
{
  "sf-cli-mcp": {
    "command": "node",
    "args": ["./mcp-server/index-simple.js"]
  }
}
```

## 🐛 Common Issues & Solutions

### Issue 1: Server Crashes on Startup
**Symptoms**: MCP logs show server crash
**Solution**: 
- Check Node.js version: `node --version` (should be 18+)
- Verify all dependencies: `npm install`

### Issue 2: Permission Denied
**Symptoms**: MCP logs show permission errors
**Solution**:
```bash
chmod +x /Volumes/SSD/loom_dynamics/sf_cli/mcp-server/index-simple.js
```

### Issue 3: Path Not Found
**Symptoms**: MCP logs show "ENOENT" errors
**Solution**: Verify the exact path exists:
```bash
ls -la /Volumes/SSD/loom_dynamics/sf_cli/mcp-server/index-simple.js
```

### Issue 4: Cursor Cache Issues
**Symptoms**: Configuration changes not taking effect
**Solution**:
1. Quit Cursor completely
2. Clear Cursor cache (location varies by OS)
3. Restart Cursor

## 📋 Debugging Commands

### Check Server Status
```bash
cd /Volumes/SSD/loom_dynamics/sf_cli/mcp-server
node test-tools.js
```

### Check Configuration
```bash
cat ~/.cursor/mcp.json
```

### Check File Permissions
```bash
ls -la /Volumes/SSD/loom_dynamics/sf_cli/mcp-server/index-simple.js
```

### Check Node.js Version
```bash
node --version
```

## 🎯 Expected Result

After following these steps, you should see:
- **"sf-cli-mcp"** with **"10 tools enabled"** in Cursor MCP settings
- All 10 tools available in Agent mode:
  - `initialize_project`
  - `generate_feature`
  - `generate_model`
  - `generate_cubit`
  - `generate_bloc`
  - `run_build_runner`
  - `generate_from_config`
  - `generate_feature_from_config`
  - `show_help`
  - `create_sample_config`

## 🆘 If Still Not Working

If you've tried all steps and still see "0 tools enabled":

1. **Check MCP Logs** for specific error messages
2. **Try a different MCP server** to verify Cursor MCP functionality
3. **Update Cursor** to the latest version
4. **Report the issue** with MCP logs to Cursor support

## 📞 Support

If you need additional help:
1. Run `node debug-mcp.js` and share the output
2. Check MCP logs in Cursor and share any error messages
3. Verify your Cursor version is up to date 