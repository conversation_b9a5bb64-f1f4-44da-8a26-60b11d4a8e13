#!/bin/bash

echo "🔄 Updating Cursor MCP Configuration for SF CLI..."

# Get the absolute path to the server
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVER_PATH="$SCRIPT_DIR/index-simple.js"

echo "✅ Server path: $SERVER_PATH"

# Check if the server file exists
if [ ! -f "$SERVER_PATH" ]; then
    echo "❌ Server file not found at: $SERVER_PATH"
    exit 1
fi

# Test the server
echo "🧪 Testing server..."
node "$SERVER_PATH" &
SERVER_PID=$!

# Wait a moment for server to start
sleep 2

# Send a test request
echo '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}' | node "$SERVER_PATH" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Server test successful"
else
    echo "❌ Server test failed"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

kill $SERVER_PID 2>/dev/null

echo ""
echo "📋 Cursor Configuration:"
echo ""
echo "1. Open Cursor settings (Cmd/Ctrl + ,)"
echo "2. Navigate to the MCP section"
echo "3. Find the 'sf-cli-mcp' entry and click the edit button"
echo "4. Update the configuration to:"
echo ""
echo "   {"
echo "     \"sf-cli-mcp\": {"
echo "       \"command\": \"node\","
echo "       \"args\": [\"$SERVER_PATH\"]"
echo "     }"
echo "   }"
echo ""
echo "5. Save the configuration"
echo "6. Restart Cursor"
echo ""
echo "🔧 After restart, you should see '10 tools enabled' instead of '0 tools enabled'"
echo ""
echo "💡 If you still see '0 tools enabled', try:"
echo "   - Disabling and re-enabling the MCP server"
echo "   - Checking Cursor's developer console for errors"
echo "   - Verifying the server path is correct"
echo "" 