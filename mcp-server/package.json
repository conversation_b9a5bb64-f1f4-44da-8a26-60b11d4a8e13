{"name": "sf-cli-mcp", "version": "1.0.0", "description": "MCP server for SF CLI - Flutter Scaffolding Tool", "type": "module", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node --watch index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "zod": "^3.22.4"}, "keywords": ["mcp", "flutter", "cli", "scaffolding", "bloc", "cubit"], "author": "SF CLI Team", "license": "MIT"}