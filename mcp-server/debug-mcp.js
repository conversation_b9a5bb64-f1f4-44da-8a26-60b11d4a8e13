import { spawn } from 'child_process';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🔍 MCP Server Debug Tool');
console.log('========================');

// Test 1: Check if the server file exists
const serverPath = join(__dirname, 'index-simple.js');
console.log(`\n1️⃣ Checking server file: ${serverPath}`);
try {
  const fs = await import('fs');
  if (fs.existsSync(serverPath)) {
    console.log('✅ Server file exists');
  } else {
    console.log('❌ Server file not found');
    process.exit(1);
  }
} catch (error) {
  console.log('❌ Error checking server file:', error.message);
  process.exit(1);
}

// Test 2: Check if Node.js can execute the server
console.log('\n2️⃣ Testing server execution...');
const child = spawn('node', [serverPath], {
  stdio: ['pipe', 'pipe', 'pipe']
});

let stdout = '';
let stderr = '';
let serverReady = false;

child.stdout.on('data', (data) => {
  stdout += data.toString();
  console.log('📤 Server stdout:', data.toString().trim());
});

child.stderr.on('data', (data) => {
  stderr += data.toString();
  console.log('⚠️  Server stderr:', data.toString().trim());
});

child.on('error', (error) => {
  console.log('❌ Server spawn error:', error.message);
});

// Wait a moment for server to initialize
setTimeout(() => {
  console.log('\n3️⃣ Testing MCP protocol...');
  
  // Send initialization request
  const initRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {
        tools: {}
      },
      clientInfo: {
        name: "cursor-debug",
        version: "1.0.0"
      }
    }
  };
  
  console.log('📤 Sending init request...');
  child.stdin.write(JSON.stringify(initRequest) + '\n');
  
  // Wait for response and send tools/list
  setTimeout(() => {
    const listToolsRequest = {
      jsonrpc: "2.0",
      id: 2,
      method: "tools/list",
      params: {}
    };
    
    console.log('📤 Sending tools/list request...');
    child.stdin.write(JSON.stringify(listToolsRequest) + '\n');
    
    // Wait for response and test a tool call
    setTimeout(() => {
      const testToolRequest = {
        jsonrpc: "2.0",
        id: 3,
        method: "tools/call",
        params: {
          name: "show_help",
          arguments: {}
        }
      };
      
      console.log('📤 Sending test tool call...');
      child.stdin.write(JSON.stringify(testToolRequest) + '\n');
      
      // Final cleanup
      setTimeout(() => {
        console.log('\n4️⃣ Test Summary:');
        console.log('================');
        console.log('Server stdout length:', stdout.length);
        console.log('Server stderr length:', stderr.length);
        
        if (stdout.includes('"tools":')) {
          const toolsMatch = stdout.match(/"tools":\s*\[([^\]]+)\]/);
          if (toolsMatch) {
            const toolsCount = (toolsMatch[1].match(/"name":/g) || []).length;
            console.log(`✅ Found ${toolsCount} tools in response`);
          } else {
            console.log('❌ No tools array found in response');
          }
        } else {
          console.log('❌ No tools response found');
        }
        
        if (stderr) {
          console.log('⚠️  Server produced stderr output');
        }
        
        console.log('\n📋 Full stdout:');
        console.log(stdout);
        
        if (stderr) {
          console.log('\n📋 Full stderr:');
          console.log(stderr);
        }
        
        child.kill();
        process.exit(0);
      }, 2000);
    }, 2000);
  }, 2000);
}, 1000); 