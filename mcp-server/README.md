# SF CLI MCP Server

This is a Model Context Protocol (MCP) server for the SF CLI - Flutter Scaffolding Tool. It allows you to use SF CLI commands directly from Cursor or other MCP-compatible applications.

## Features

The MCP server provides the following tools:

- **initialize_project**: Initialize a new Flutter project with SF CLI structure
- **generate_feature**: Generate a new feature with complete folder structure
- **generate_model**: Generate Dart model classes from JSON files
- **generate_cubit**: Generate a new Cubit class with state management
- **generate_bloc**: Generate a new BLoC class with event and state management
- **run_build_runner**: Run build_runner to generate code
- **generate_from_config**: Generate code from configuration file
- **generate_feature_from_config**: Generate complete feature from enhanced configuration file
- **show_help**: Show SF CLI help information
- **create_sample_config**: Create sample configuration files

## Installation

1. Navigate to the mcp-server directory:
   ```bash
   cd mcp-server
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

## Setup in Cursor

1. Open Cursor settings
2. Go to the MCP section
3. Add a new MCP server with the following configuration:

```json
{
  "sf-cli-mcp": {
    "command": "node",
    "args": ["/absolute/path/to/sf_cli/mcp-server/index.js"]
  }
}
```

Replace `/absolute/path/to/sf_cli` with the actual path to your sf_cli project.

## Usage Examples

Once configured, you can use the MCP tools in Cursor's Agent mode:

### Initialize a new project
```
Initialize a new Flutter project with SF CLI structure
```

### Generate a feature
```
Generate a user_profile feature with freezed support
```

### Generate a model
```
Generate a model from the user.json file
```

### Generate a Cubit
```
Generate a settings cubit with freezed state
```

### Generate a BLoC
```
Generate an auth bloc with freezed events
```

### Run build runner
```
Run the build runner to generate code
```

### Create a sample configuration
```
Create a sample enhanced configuration file for a product catalog feature
```

## Configuration Files

The MCP server can work with two types of configuration files:

### Basic Configuration
Used for simple model and API generation:
```json
{
  "user_model": {
    "model_class_relative_path": "lib/models/user.json",
    "end_point": "/api/users",
    "method": "get",
    "function_name": "getUsers",
    "parameters": {
      "page": "int",
      "limit": "int",
      "search": "String"
    }
  }
}
```

### Enhanced Configuration
Used for complete feature generation:
```json
{
  "feature_name": "user_profile",
  "description": "A complete user profile feature",
  "models": [
    {
      "name": "user",
      "json_file_path": "lib/models/user.json",
      "use_freezed": true
    }
  ],
  "endpoints": [
    {
      "name": "get_user_profile",
      "endpoint": "/api/users/{id}",
      "method": "GET",
      "function_name": "getUserProfile",
      "parameters": {
        "id": "int"
      },
      "return_type": "ResponseResult<User>"
    }
  ],
  "ui_config": {
    "has_list_screen": false,
    "has_detail_screen": true,
    "has_form_screen": true,
    "custom_widgets": [
      "profile_card",
      "avatar_upload"
    ]
  }
}
```

## Development

To run the MCP server in development mode with auto-restart:

```bash
npm run dev
```

## Troubleshooting

1. **Command not found**: Make sure the path to the sf_cli binary is correct in the MCP configuration
2. **Permission denied**: Ensure the script has execute permissions
3. **Dart not found**: Make sure Dart is installed and available in your PATH

## Requirements

- Node.js 18+
- Dart SDK
- SF CLI installed or available in the project directory

## License

MIT License - same as the main SF CLI project 