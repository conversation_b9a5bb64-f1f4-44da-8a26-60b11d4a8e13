import { spawn } from 'child_process';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Test the MCP server
async function testMcpServer() {
  const serverPath = join(__dirname, 'index.js');
  const child = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let stdout = '';
  let stderr = '';

  child.stdout.on('data', (data) => {
    stdout += data.toString();
    console.log('Server output:', data.toString());
  });

  child.stderr.on('data', (data) => {
    stderr += data.toString();
    console.error('Server error:', data.toString());
  });

  // Send a simple ping request
  const pingRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "ping",
    params: {}
  };

  child.stdin.write(JSON.stringify(pingRequest) + '\n');

  // Wait a bit and then close
  setTimeout(() => {
    child.kill();
    console.log('Test completed');
  }, 2000);
}

testMcpServer().catch(console.error); 