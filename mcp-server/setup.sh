#!/bin/bash

echo "🚀 Setting up SF CLI MCP Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Get the absolute path
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVER_PATH="$SCRIPT_DIR/index.js"

echo ""
echo "🎉 SF CLI MCP Server setup completed!"
echo ""
echo "📋 To use this MCP server in Cursor:"
echo ""
echo "1. Open Cursor settings"
echo "2. Go to the MCP section"
echo "3. Add a new MCP server with this configuration:"
echo ""
echo "   {"
echo "     \"sf-cli-mcp\": {"
echo "       \"command\": \"node\","
echo "       \"args\": [\"$SCRIPT_DIR/index-simple.js\"]"
echo "     }"
echo "   }"
echo ""
echo "4. Restart Cursor"
echo ""
echo "🔧 Available tools:"
echo "   - initialize_project: Initialize a new Flutter project"
echo "   - generate_feature: Generate a new feature"
echo "   - generate_model: Generate models from JSON"
echo "   - generate_cubit: Generate a Cubit"
echo "   - generate_bloc: Generate a BLoC"
echo "   - run_build_runner: Run build runner"
echo "   - generate_from_config: Generate from config file"
echo "   - generate_feature_from_config: Generate feature from config"
echo "   - show_help: Show SF CLI help"
echo "   - create_sample_config: Create sample config files"
echo ""
echo "💡 Example usage in Cursor Agent mode:"
echo "   \"Generate a user_profile feature with freezed support\""
echo "   \"Initialize a new Flutter project with SF CLI structure\""
echo "   \"Create a sample enhanced configuration for a product catalog\""
echo "" 