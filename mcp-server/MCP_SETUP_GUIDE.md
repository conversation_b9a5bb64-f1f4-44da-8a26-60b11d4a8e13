# SF CLI MCP Server Setup Guide

This guide will help you set up and use the SF CLI MCP (Model Context Protocol) server in Cursor.

## What is MCP?

Model Context Protocol (MCP) is a standard that allows AI assistants like Cursor to interact with external tools and services. This MCP server provides access to all SF CLI functionality directly from <PERSON>ursor's Agent mode.

## Prerequisites

- Node.js 18+ installed
- npm installed
- SF CLI project available
- Cursor IDE

## Quick Setup

1. **Navigate to the MCP server directory:**
   ```bash
   cd mcp-server
   ```

2. **Run the setup script:**
   ```bash
   ./setup.sh
   ```

3. **Follow the instructions provided by the setup script**

## Manual Setup

If you prefer to set up manually:

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Get the absolute path to the server:**
   ```bash
   pwd
   # Note the full path to the mcp-server directory
   ```

## Cursor Configuration

1. Open Cursor settings (Cmd/Ctrl + ,)
2. Navigate to the MCP section
3. Add a new MCP server with this configuration:

```json
{
  "sf-cli-mcp": {
    "command": "node",
    "args": ["/absolute/path/to/sf_cli/mcp-server/index.js"]
  }
}
```

Replace `/absolute/path/to/sf_cli` with the actual path to your sf_cli project.

4. Restart Cursor

## Available Tools

The MCP server provides the following tools:

### Project Management
- **`initialize_project`**: Initialize a new Flutter project with SF CLI structure

### Feature Generation
- **`generate_feature`**: Generate a new feature with complete folder structure
- **`generate_feature_from_config`**: Generate complete feature from enhanced configuration file

### Model Generation
- **`generate_model`**: Generate Dart model classes from JSON files

### State Management
- **`generate_cubit`**: Generate a new Cubit class with state management
- **`generate_bloc`**: Generate a new BLoC class with event and state management

### Code Generation
- **`run_build_runner`**: Run build_runner to generate code
- **`generate_from_config`**: Generate code from configuration file

### Utilities
- **`show_help`**: Show SF CLI help information
- **`create_sample_config`**: Create sample configuration files

## Usage Examples

Once configured, you can use these tools in Cursor's Agent mode:

### Initialize a new project
```
Initialize a new Flutter project with SF CLI structure
```

### Generate a feature
```
Generate a user_profile feature with freezed support
```

### Generate a model
```
Generate a model from the user.json file
```

### Generate state management
```
Generate a settings cubit with freezed state
Generate an auth bloc with freezed events
```

### Run build runner
```
Run the build runner to generate code
```

### Create configuration files
```
Create a sample enhanced configuration file for a product catalog feature
```

## Configuration Files

The MCP server supports two types of configuration files:

### Basic Configuration
For simple model and API generation:
```json
{
  "user_model": {
    "model_class_relative_path": "lib/models/user.json",
    "end_point": "/api/users",
    "method": "get",
    "function_name": "getUsers",
    "parameters": {
      "page": "int",
      "limit": "int",
      "search": "String"
    }
  }
}
```

### Enhanced Configuration
For complete feature generation:
```json
{
  "feature_name": "user_profile",
  "description": "A complete user profile feature",
  "models": [
    {
      "name": "user",
      "json_file_path": "lib/models/user.json",
      "use_freezed": true
    }
  ],
  "endpoints": [
    {
      "name": "get_user_profile",
      "endpoint": "/api/users/{id}",
      "method": "GET",
      "function_name": "getUserProfile",
      "parameters": {
        "id": "int"
      },
      "return_type": "ResponseResult<User>"
    }
  ],
  "ui_config": {
    "has_list_screen": false,
    "has_detail_screen": true,
    "has_form_screen": true,
    "custom_widgets": [
      "profile_card",
      "avatar_upload"
    ]
  }
}
```

## Troubleshooting

### Common Issues

1. **"Command not found" error**
   - Make sure the path to the sf_cli binary is correct
   - Ensure SF CLI is installed or available in the project directory

2. **"Permission denied" error**
   - Make sure the script has execute permissions
   - Run `chmod +x setup.sh` if needed

3. **"Dart not found" error**
   - Make sure Dart SDK is installed and available in your PATH
   - Verify Dart installation with `dart --version`

4. **MCP server not connecting**
   - Check that the path in Cursor settings is correct
   - Ensure the server file exists at the specified path
   - Restart Cursor after configuration changes

### Debug Mode

To run the MCP server in debug mode:
```bash
node --inspect index.js
```

### Testing

To test the MCP server manually:
```bash
node test.js
```

## Development

To contribute to the MCP server:

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Run in development mode:**
   ```bash
   npm run dev
   ```

3. **Make changes to `index.js`**
4. **Test your changes**
5. **Submit a pull request**

## Requirements

- Node.js 18+
- Dart SDK
- SF CLI installed or available in the project directory
- Cursor IDE

## License

MIT License - same as the main SF CLI project

## Support

For issues and questions:
- Check the [SF CLI documentation](https://naveenld024.github.io/sf_cli/)
- Visit the [SF CLI repository](https://github.com/naveenld024/sf_cli)
- Open an issue on GitHub 