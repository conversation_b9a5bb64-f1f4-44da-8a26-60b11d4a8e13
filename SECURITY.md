# Security Policy

## Supported Versions

We actively support the following versions of SF CLI with security updates:

| Version | Supported          |
| ------- | ------------------ |
| 1.0.x   | :white_check_mark: |
| < 1.0   | :x:                |

## Reporting a Vulnerability

We take the security of SF CLI seriously. If you believe you have found a security vulnerability, please report it to us as described below.

### How to Report

**Please do not report security vulnerabilities through public GitHub issues.**

Instead, please report them via email to: **<EMAIL>**

Please include the following information in your report:

- Type of issue (e.g. buffer overflow, SQL injection, cross-site scripting, etc.)
- Full paths of source file(s) related to the manifestation of the issue
- The location of the affected source code (tag/branch/commit or direct URL)
- Any special configuration required to reproduce the issue
- Step-by-step instructions to reproduce the issue
- Proof-of-concept or exploit code (if possible)
- Impact of the issue, including how an attacker might exploit the issue

### What to Expect

- **Acknowledgment**: We will acknowledge receipt of your vulnerability report within 48 hours.
- **Initial Assessment**: We will provide an initial assessment of the vulnerability within 5 business days.
- **Regular Updates**: We will keep you informed of our progress towards resolving the issue.
- **Resolution**: We aim to resolve critical vulnerabilities within 30 days of the initial report.

### Disclosure Policy

- We will coordinate with you on the timing of public disclosure.
- We prefer to fully address the issue before any public disclosure.
- We will credit you in our security advisory (unless you prefer to remain anonymous).

## Security Best Practices

When using SF CLI, please follow these security best practices:

### For Users

1. **Keep SF CLI Updated**: Always use the latest version of SF CLI to ensure you have the latest security fixes.

2. **Verify Downloads**: Only download SF CLI from official sources:
   - [pub.dev](https://pub.dev/packages/sf_cli)
   - [GitHub Releases](https://github.com/naveenld024/sf_cli/releases)

3. **Review Generated Code**: Always review the code generated by SF CLI before using it in production.

4. **Secure Your Dependencies**: Regularly update your Flutter and Dart dependencies.

5. **Environment Security**: 
   - Don't run SF CLI with elevated privileges unless necessary
   - Be cautious when running SF CLI in shared environments

### For Contributors

1. **Code Review**: All code changes must be reviewed before merging.

2. **Dependency Management**: 
   - Keep dependencies up to date
   - Regularly audit dependencies for known vulnerabilities
   - Use `dart pub deps` to check for security advisories

3. **Input Validation**: Always validate and sanitize user inputs.

4. **File System Operations**: Be careful with file system operations to prevent path traversal attacks.

5. **Error Handling**: Don't expose sensitive information in error messages.

## Security Features

SF CLI includes the following security features:

- **Input Validation**: All user inputs are validated and sanitized
- **Safe File Operations**: File operations are performed safely to prevent path traversal
- **No Network Operations**: SF CLI doesn't make network requests (except for pub.dev when installing)
- **Minimal Permissions**: SF CLI only requires the minimum necessary file system permissions

## Known Security Considerations

- **Generated Code**: SF CLI generates code based on user input. Users should review generated code before use.
- **File System Access**: SF CLI requires write access to your project directory to generate files.
- **JSON Parsing**: SF CLI parses JSON files provided by users. Malformed JSON files are handled safely.

## Security Updates

Security updates will be released as patch versions and will be clearly marked in the changelog. We recommend:

- Subscribing to release notifications on GitHub
- Following our [documentation](https://naveenld024.github.io/sf_cli/) for update instructions
- Joining our [GitHub Discussions](https://github.com/naveenld024/sf_cli/discussions) for security announcements

## Contact

For any security-related questions or concerns, please contact:

- **Email**: <EMAIL>
- **GitHub**: [@naveenld024](https://github.com/naveenld024)

## Acknowledgments

We would like to thank the following individuals for responsibly disclosing security vulnerabilities:

<!-- This section will be updated as we receive and resolve security reports -->

*No security vulnerabilities have been reported yet.*

---

Thank you for helping keep SF CLI and our users safe!
