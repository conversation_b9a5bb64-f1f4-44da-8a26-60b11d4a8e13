{"roots": ["sf_cli"], "packages": [{"name": "sf_cli", "version": "1.0.3", "dependencies": ["args", "path", "process_run"], "devDependencies": ["lints", "test"]}, {"name": "test", "version": "1.25.2", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "lints", "version": "3.0.0", "dependencies": []}, {"name": "path", "version": "1.9.0", "dependencies": []}, {"name": "process_run", "version": "0.12.5+3", "dependencies": ["args", "charcode", "collection", "meta", "path", "pub_semver", "string_scanner", "synchronized", "yaml"]}, {"name": "args", "version": "2.5.0", "dependencies": []}, {"name": "yaml", "version": "3.1.2", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "web_socket_channel", "version": "2.4.5", "dependencies": ["async", "crypto", "stream_channel", "web"]}, {"name": "typed_data", "version": "1.3.2", "dependencies": ["collection"]}, {"name": "test_core", "version": "0.6.0", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "test_api", "version": "0.7.0", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "stream_channel", "version": "2.1.2", "dependencies": ["async"]}, {"name": "stack_trace", "version": "1.11.1", "dependencies": ["path"]}, {"name": "source_span", "version": "1.10.0", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "shelf_web_socket", "version": "1.0.4", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf_static", "version": "1.1.2", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "shelf", "version": "1.4.1", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.1.0", "dependencies": ["path"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "matcher", "version": "0.12.16+1", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "js", "version": "0.7.1", "dependencies": []}, {"name": "io", "version": "1.0.4", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.1", "dependencies": ["async"]}, {"name": "coverage", "version": "1.8.0", "dependencies": ["args", "logging", "package_config", "path", "source_maps", "stack_trace", "vm_service"]}, {"name": "collection", "version": "1.18.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.1", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.11.0", "dependencies": ["collection", "meta"]}, {"name": "analyzer", "version": "6.4.1", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "synchronized", "version": "3.1.0+1", "dependencies": []}, {"name": "pub_semver", "version": "2.1.4", "dependencies": ["collection", "meta"]}, {"name": "meta", "version": "1.15.0", "dependencies": []}, {"name": "string_scanner", "version": "1.2.0", "dependencies": ["source_span"]}, {"name": "charcode", "version": "1.3.1", "dependencies": []}, {"name": "logging", "version": "1.2.0", "dependencies": []}, {"name": "web", "version": "0.5.1", "dependencies": []}, {"name": "crypto", "version": "3.0.3", "dependencies": ["typed_data"]}, {"name": "vm_service", "version": "14.2.1", "dependencies": []}, {"name": "source_maps", "version": "0.10.12", "dependencies": ["source_span"]}, {"name": "source_map_stack_trace", "version": "2.1.1", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "glob", "version": "2.1.2", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "term_glyph", "version": "1.2.1", "dependencies": []}, {"name": "mime", "version": "1.0.5", "dependencies": []}, {"name": "http_parser", "version": "4.0.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "convert", "version": "3.1.1", "dependencies": ["typed_data"]}, {"name": "watcher", "version": "1.1.0", "dependencies": ["async", "path"]}, {"name": "_fe_analyzer_shared", "version": "67.0.0", "dependencies": ["meta"]}, {"name": "file", "version": "7.0.0", "dependencies": ["meta", "path"]}], "configVersion": 1}